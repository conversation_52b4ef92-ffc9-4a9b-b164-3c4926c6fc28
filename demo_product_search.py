"""
商品搜索系统演示脚本
展示系统的主要功能
"""

from utils.product_data_manager import ProductDataManager
from utils.semantic_search_engine import SemanticSearchEngine
import pandas as pd

def demo_data_management():
    """演示数据管理功能"""
    print("=== 数据管理功能演示 ===")
    
    manager = ProductDataManager()
    
    # 显示当前商品数据
    print("\n1. 当前商品数据:")
    df = manager.load_products()
    print(f"共有 {len(df)} 个商品")
    
    # 显示前5个商品
    print("\n前5个商品:")
    for i, row in df.head().iterrows():
        print(f"  {row['product_id']}. {row['product_name_zh']} - {row['location']}")
    
    # 演示添加商品
    print("\n2. 添加新商品演示:")
    new_product = {
        'product_name_zh': '演示商品',
        'product_name_es': 'producto demo',
        'location': '演示区，货架Demo',
        'tags': '演示,demo,测试'
    }
    
    success, message = manager.add_product(new_product)
    print(f"添加结果: {message}")
    
    if success:
        # 显示更新后的数据
        df = manager.load_products()
        print(f"现在共有 {len(df)} 个商品")
        
        # 删除刚添加的商品（清理）
        last_id = df['product_id'].astype(int).max()
        success, message = manager.delete_product(last_id)
        print(f"清理结果: {message}")

def demo_search_functionality():
    """演示搜索功能（基础版本，不需要模型）"""
    print("\n=== 搜索功能演示 ===")
    
    # 初始化搜索引擎
    search_engine = SemanticSearchEngine()
    
    # 获取索引信息
    info = search_engine.get_index_info()
    print(f"\n搜索引擎状态:")
    print(f"  模型: {info['model_name']}")
    print(f"  索引存在: {info['index_exists']}")
    print(f"  商品数量: {info['product_count']}")
    
    if not info['index_exists']:
        print("\n注意: 索引不存在，需要在实际使用时构建索引")
        print("由于模型较大，这里不进行实际的索引构建演示")
    
    # 演示搜索文本创建功能
    print("\n演示搜索文本创建:")
    manager = ProductDataManager()
    products = manager.get_all_products_for_search()
    
    if products:
        sample_product = products[0]
        search_text = search_engine._create_search_text(sample_product)
        print(f"商品: {sample_product['product_name_zh']}")
        print(f"搜索文本: {search_text}")

def demo_csv_structure():
    """演示CSV文件结构"""
    print("\n=== CSV文件结构演示 ===")
    
    manager = ProductDataManager()
    df = manager.load_products()
    
    print(f"\nCSV文件路径: {manager.csv_file_path}")
    print(f"必需列: {manager.required_columns}")
    print(f"可选列: {manager.optional_columns}")
    
    print(f"\n数据结构:")
    print(df.dtypes)
    
    print(f"\n数据示例:")
    print(df.head(3).to_string(index=False))

def demo_multilingual_examples():
    """演示多语言示例"""
    print("\n=== 多语言支持演示 ===")
    
    manager = ProductDataManager()
    df = manager.load_products()
    
    print("\n多语言商品示例:")
    for i, row in df.head(5).iterrows():
        print(f"\n{row['product_id']}. 中文: {row['product_name_zh']}")
        if row['product_name_es']:
            print(f"   西班牙语: {row['product_name_es']}")
        print(f"   位置: {row['location']}")
        if row['tags']:
            print(f"   标签: {row['tags']}")
    
    print("\n支持的搜索查询示例:")
    examples = [
        ("中文", ["水", "苹果", "菜刀", "洗发水"]),
        ("西班牙语", ["agua", "manzana", "cuchillo", "champú"]),
        ("英语", ["water", "apple", "knife", "shampoo"]),
        ("语义描述", ["用来切菜的东西", "洗头发的", "解渴的饮品"])
    ]
    
    for lang, queries in examples:
        print(f"\n{lang}:")
        for query in queries:
            print(f"  - {query}")

def main():
    """主演示函数"""
    print("🔍 商品搜索系统功能演示")
    print("=" * 50)
    
    try:
        # 演示数据管理
        demo_data_management()
        
        # 演示CSV结构
        demo_csv_structure()
        
        # 演示多语言支持
        demo_multilingual_examples()
        
        # 演示搜索功能
        demo_search_functionality()
        
        print("\n" + "=" * 50)
        print("✅ 演示完成!")
        
        print("\n📋 系统功能总结:")
        print("1. ✅ 商品数据管理 (增删查改)")
        print("2. ✅ CSV文件存储")
        print("3. ✅ 多语言支持 (中文、西班牙语、英语)")
        print("4. ✅ 语义搜索引擎架构")
        print("5. ✅ Streamlit用户界面")
        print("6. ✅ 向量索引管理")
        
        print("\n🚀 使用方法:")
        print("1. 运行 Streamlit 应用: streamlit run streamlit_app.py")
        print("2. 访问商品管理页面添加/管理商品")
        print("3. 在索引管理中构建搜索索引")
        print("4. 使用智能搜索页面进行多语言搜索")
        
        print("\n⚠️  注意事项:")
        print("- 首次使用需要下载语言模型 (约470MB)")
        print("- 建议在有足够内存的环境中运行")
        print("- 添加或删除商品后需要重建索引")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查依赖包是否正确安装")

if __name__ == "__main__":
    main()
