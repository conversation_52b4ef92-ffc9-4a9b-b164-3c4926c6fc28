"""
简单测试语义搜索引擎
"""

import os
import sys

def test_imports():
    """测试导入是否正常"""
    print("=== 测试导入 ===")
    
    try:
        print("1. 测试pandas导入...")
        import pandas as pd
        print("   pandas导入成功")
        
        print("2. 测试numpy导入...")
        import numpy as np
        print("   numpy导入成功")
        
        print("3. 测试faiss导入...")
        import faiss
        print("   faiss导入成功")
        
        print("4. 测试sentence-transformers导入...")
        from sentence_transformers import SentenceTransformer
        print("   sentence-transformers导入成功")
        
        print("5. 测试自定义模块导入...")
        from utils.product_data_manager import ProductDataManager
        print("   ProductDataManager导入成功")
        
        from utils.semantic_search_engine import SemanticSearchEngine
        print("   SemanticSearchEngine导入成功")
        
        print("\n所有导入测试通过!")
        return True
        
    except Exception as e:
        print(f"导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        # 测试数据管理器
        print("1. 测试数据管理器...")
        from utils.product_data_manager import ProductDataManager
        manager = ProductDataManager()
        products = manager.get_all_products_for_search()
        print(f"   加载了 {len(products)} 个商品")
        
        # 测试搜索引擎初始化
        print("2. 测试搜索引擎初始化...")
        from utils.semantic_search_engine import SemanticSearchEngine
        search_engine = SemanticSearchEngine()
        print("   搜索引擎初始化成功")
        
        # 获取索引信息
        info = search_engine.get_index_info()
        print(f"   模型: {info['model_name']}")
        print(f"   索引存在: {info['index_exists']}")
        
        print("\n基本功能测试通过!")
        return True
        
    except Exception as e:
        print(f"基本功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始简单测试...")
    
    # 测试导入
    if not test_imports():
        print("导入测试失败，退出")
        return
    
    # 测试基本功能
    if not test_basic_functionality():
        print("基本功能测试失败，退出")
        return
    
    print("\n=== 所有测试通过 ===")
    print("注意：由于模型较大，完整的搜索功能测试需要在有足够内存的环境中进行")

if __name__ == "__main__":
    main()
