"""
测试商品数据管理器
"""

from utils.product_data_manager import ProductDataManager

def test_product_manager():
    """测试商品数据管理器的基本功能"""
    
    # 初始化管理器
    manager = ProductDataManager()
    
    print("=== 测试商品数据管理器 ===")
    
    # 测试加载数据
    print("\n1. 加载商品数据:")
    df = manager.load_products()
    print(f"加载了 {len(df)} 条商品数据")
    print(df.head())
    
    # 测试获取特定商品
    print("\n2. 获取商品ID为1的商品:")
    product = manager.get_product_by_id(1)
    if product:
        print(f"商品名称: {product['product_name_zh']}")
        print(f"位置: {product['location']}")
        print(f"标签: {product['tags']}")
    
    # 测试添加新商品
    print("\n3. 添加新商品:")
    new_product = {
        'product_name_zh': '测试商品',
        'product_name_es': 'producto de prueba',
        'location': '测试区，货架Z',
        'tags': '测试,test,prueba'
    }
    
    success, message = manager.add_product(new_product)
    print(f"添加结果: {message}")
    
    if success:
        # 重新加载数据查看结果
        df = manager.load_products()
        print(f"现在有 {len(df)} 条商品数据")
        print("最新添加的商品:")
        print(df.tail(1))
    
    # 测试获取所有商品用于搜索
    print("\n4. 获取所有商品用于搜索:")
    all_products = manager.get_all_products_for_search()
    print(f"获取了 {len(all_products)} 条商品数据用于搜索")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_product_manager()
