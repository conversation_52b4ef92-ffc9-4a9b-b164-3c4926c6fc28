"""
测试语义搜索引擎
"""

from utils.semantic_search_engine import SemanticSearchEngine
from utils.product_data_manager import ProductDataManager

def test_search_engine():
    """测试语义搜索引擎的基本功能"""
    
    print("=== 测试语义搜索引擎 ===")
    
    # 初始化搜索引擎
    print("\n1. 初始化搜索引擎...")
    search_engine = SemanticSearchEngine()
    
    # 获取索引信息
    print("\n2. 检查索引状态:")
    info = search_engine.get_index_info()
    print(f"模型名称: {info['model_name']}")
    print(f"索引存在: {info['index_exists']}")
    print(f"商品数量: {info['product_count']}")
    
    # 构建索引
    print("\n3. 构建搜索索引...")
    success = search_engine.build_index(force_rebuild=True)
    if success:
        print("索引构建成功!")
        
        # 更新索引信息
        info = search_engine.get_index_info()
        print(f"索引维度: {info['index_dimension']}")
        print(f"商品数量: {info['product_count']}")
    else:
        print("索引构建失败!")
        return
    
    # 测试搜索功能
    print("\n4. 测试搜索功能:")
    
    # 测试用例
    test_queries = [
        "水",
        "agua",
        "water",
        "用来切菜的东西",
        "cuchillo",
        "knife",
        "水果",
        "fruta",
        "fruit",
        "洗头发的",
        "champú",
        "shampoo"
    ]
    
    for query in test_queries:
        print(f"\n搜索查询: '{query}'")
        results = search_engine.search(query, top_k=3)
        
        if results:
            for i, (product, score) in enumerate(results, 1):
                print(f"  {i}. {product['product_name_zh']} ({product.get('product_name_es', '')}) - 相似度: {score:.3f}")
                print(f"     位置: {product['location']}")
        else:
            print("  没有找到相关结果")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_search_engine()
