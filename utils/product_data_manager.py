"""
商品数据管理模块
负责商品数据的读取、写入、验证等基础功能
"""

import pandas as pd
import os
from typing import List, Dict, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductDataManager:
    """商品数据管理器"""
    
    def __init__(self, csv_file_path: str = "data/products.csv"):
        """
        初始化商品数据管理器
        
        Args:
            csv_file_path: CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.required_columns = ['product_id', 'product_name_zh', 'location']
        self.optional_columns = ['product_name_es', 'tags']
        self.all_columns = self.required_columns + self.optional_columns
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(csv_file_path), exist_ok=True)
        
        # 如果文件不存在，创建空的CSV文件
        if not os.path.exists(csv_file_path):
            self._create_empty_csv()
    
    def _create_empty_csv(self):
        """创建空的CSV文件"""
        empty_df = pd.DataFrame(columns=self.all_columns)
        empty_df.to_csv(self.csv_file_path, index=False, encoding='utf-8')
        logger.info(f"创建空的商品数据文件: {self.csv_file_path}")
    
    def load_products(self) -> pd.DataFrame:
        """
        加载商品数据
        
        Returns:
            包含商品数据的DataFrame
        """
        try:
            if os.path.exists(self.csv_file_path):
                df = pd.read_csv(self.csv_file_path, encoding='utf-8')
                
                # 确保所有必需的列都存在
                for col in self.all_columns:
                    if col not in df.columns:
                        df[col] = ''
                
                # 重新排序列
                df = df[self.all_columns]
                
                logger.info(f"成功加载 {len(df)} 条商品数据")
                return df
            else:
                logger.warning(f"商品数据文件不存在: {self.csv_file_path}")
                return pd.DataFrame(columns=self.all_columns)
        except Exception as e:
            logger.error(f"加载商品数据失败: {e}")
            return pd.DataFrame(columns=self.all_columns)
    
    def save_products(self, df: pd.DataFrame) -> bool:
        """
        保存商品数据
        
        Args:
            df: 要保存的商品数据DataFrame
            
        Returns:
            保存是否成功
        """
        try:
            # 验证数据格式
            if not self._validate_dataframe(df):
                return False
            
            # 确保列的顺序正确
            df = df[self.all_columns]
            
            # 保存到CSV文件
            df.to_csv(self.csv_file_path, index=False, encoding='utf-8')
            logger.info(f"成功保存 {len(df)} 条商品数据到 {self.csv_file_path}")
            return True
        except Exception as e:
            logger.error(f"保存商品数据失败: {e}")
            return False
    
    def add_product(self, product_data: Dict[str, str]) -> Tuple[bool, str]:
        """
        添加新商品
        
        Args:
            product_data: 商品数据字典
            
        Returns:
            (是否成功, 消息)
        """
        try:
            # 验证必需字段
            validation_result = self._validate_product_data(product_data)
            if not validation_result[0]:
                return validation_result
            
            # 加载现有数据
            df = self.load_products()
            
            # 生成新的product_id
            if len(df) == 0:
                new_id = 1
            else:
                new_id = df['product_id'].astype(int).max() + 1
            
            # 准备新行数据
            new_row = {col: '' for col in self.all_columns}
            new_row['product_id'] = new_id
            new_row.update(product_data)
            
            # 添加新行
            new_df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
            
            # 保存数据
            if self.save_products(new_df):
                return True, f"成功添加商品，ID: {new_id}"
            else:
                return False, "保存商品数据失败"
                
        except Exception as e:
            logger.error(f"添加商品失败: {e}")
            return False, f"添加商品失败: {str(e)}"
    
    def delete_product(self, product_id: int) -> Tuple[bool, str]:
        """
        删除商品
        
        Args:
            product_id: 商品ID
            
        Returns:
            (是否成功, 消息)
        """
        try:
            df = self.load_products()
            
            # 检查商品是否存在
            if product_id not in df['product_id'].astype(int).values:
                return False, f"商品ID {product_id} 不存在"
            
            # 删除商品
            df = df[df['product_id'].astype(int) != product_id]
            
            # 保存数据
            if self.save_products(df):
                return True, f"成功删除商品ID: {product_id}"
            else:
                return False, "保存数据失败"
                
        except Exception as e:
            logger.error(f"删除商品失败: {e}")
            return False, f"删除商品失败: {str(e)}"
    
    def get_product_by_id(self, product_id: int) -> Optional[Dict]:
        """
        根据ID获取商品信息
        
        Args:
            product_id: 商品ID
            
        Returns:
            商品信息字典，如果不存在返回None
        """
        try:
            df = self.load_products()
            product_rows = df[df['product_id'].astype(int) == product_id]
            
            if len(product_rows) > 0:
                return product_rows.iloc[0].to_dict()
            else:
                return None
        except Exception as e:
            logger.error(f"获取商品信息失败: {e}")
            return None
    
    def _validate_dataframe(self, df: pd.DataFrame) -> bool:
        """
        验证DataFrame格式
        
        Args:
            df: 要验证的DataFrame
            
        Returns:
            是否有效
        """
        # 检查必需的列是否存在
        for col in self.required_columns:
            if col not in df.columns:
                logger.error(f"缺少必需的列: {col}")
                return False
        
        return True
    
    def _validate_product_data(self, product_data: Dict[str, str]) -> Tuple[bool, str]:
        """
        验证商品数据
        
        Args:
            product_data: 商品数据字典
            
        Returns:
            (是否有效, 错误消息)
        """
        # 检查必需字段
        required_fields = ['product_name_zh', 'location']
        for field in required_fields:
            if field not in product_data or not product_data[field].strip():
                return False, f"缺少必需字段: {field}"
        
        return True, "验证通过"
    
    def get_all_products_for_search(self) -> List[Dict]:
        """
        获取所有商品数据用于搜索
        
        Returns:
            商品数据列表
        """
        df = self.load_products()
        return df.to_dict('records')
