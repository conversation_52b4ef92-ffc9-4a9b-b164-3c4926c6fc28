# 🔍 多语言语义商品定位系统

基于 Streamlit 的智能商品搜索系统，支持中文、西班牙语、英语等多语言语义搜索，帮助大型百货商店的员工和顾客快速找到商品位置。

## ✨ 主要特性

- 🌍 **多语言支持**: 支持中文、西班牙语、英语等多种语言
- 🧠 **语义搜索**: 基于 sentence-transformers 的智能语义理解
- ⚡ **高效检索**: 使用 Faiss 向量数据库实现毫秒级搜索
- 📱 **友好界面**: 基于 Streamlit 的直观 Web 界面
- 📊 **数据管理**: 完整的商品信息增删改查功能
- 🔧 **索引管理**: 自动化的搜索索引构建和维护

## 🛠️ 技术栈

- **应用框架**: Streamlit
- **数据处理**: Pandas
- **语义搜索**: sentence-transformers (paraphrase-multilingual-MiniLM-L12-v2)
- **向量搜索**: faiss-cpu
- **数据存储**: CSV 文件

## 📦 安装依赖

```bash
pip install streamlit pandas sentence-transformers faiss-cpu numpy
```

## 🚀 快速开始

### 1. 启动应用

```bash
streamlit run streamlit_app.py
```

### 2. 访问功能页面

- **商品搜索**: 主要的搜索界面，支持多语言语义搜索
- **商品管理**: 管理商品信息，支持增删改查操作
- **索引管理**: 构建和维护搜索索引

### 3. 首次使用

1. 访问"商品管理"页面查看示例数据
2. 在"索引管理"中点击"重建索引"
3. 等待模型下载和索引构建完成
4. 在"商品搜索"页面开始搜索

## 📋 数据结构

商品数据存储在 `data/products.csv` 文件中，包含以下字段：

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| product_id | 整数 | 是 | 商品唯一标识符 |
| product_name_zh | 文本 | 是 | 商品中文名称 |
| product_name_es | 文本 | 否 | 商品西班牙语名称 |
| location | 文本 | 是 | 商品在店内的位置 |
| tags | 文本 | 否 | 相关标签，用逗号分隔 |

## 🔍 搜索示例

### 中文搜索
- "水" → 找到矿泉水
- "用来切菜的东西" → 找到菜刀
- "洗头发的" → 找到洗发水

### 西班牙语搜索
- "agua" → 找到矿泉水
- "cuchillo" → 找到菜刀
- "champú" → 找到洗发水

### 英语搜索
- "water" → 找到矿泉水
- "knife" → 找到菜刀
- "shampoo" → 找到洗发水

## 📁 项目结构

```
├── streamlit_app.py              # 主应用入口
├── pages/tools/
│   ├── product_search.py         # 智能搜索页面
│   └── product_management.py     # 商品管理页面
├── utils/
│   ├── product_data_manager.py   # 商品数据管理器
│   └── semantic_search_engine.py # 语义搜索引擎
├── data/
│   ├── products.csv              # 商品数据文件
│   ├── products.faiss            # Faiss 索引文件
│   └── product_embeddings.pkl    # 向量嵌入文件
├── notes/
│   └── product_search_development_log.md  # 开发记录
├── demo_product_search.py        # 功能演示脚本
└── README_PRODUCT_SEARCH.md      # 项目说明文档
```

## 🧪 测试

运行演示脚本查看系统功能：

```bash
python demo_product_search.py
```

运行基础功能测试：

```bash
python test_search_simple.py
```

## ⚠️ 注意事项

1. **首次使用**: 需要下载约 470MB 的预训练模型
2. **内存要求**: 建议至少 2GB 可用内存
3. **索引更新**: 添加或删除商品后需要重建索引
4. **数据备份**: 定期备份 `data/products.csv` 文件

## 🔧 配置说明

### 模型配置
默认使用 `paraphrase-multilingual-MiniLM-L12-v2` 模型，可在 `SemanticSearchEngine` 类中修改：

```python
search_engine = SemanticSearchEngine(
    model_name="your-preferred-model"
)
```

### 数据文件路径
可在初始化时指定自定义路径：

```python
manager = ProductDataManager(csv_file_path="custom/path/products.csv")
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 支持

如有问题或建议，请查看开发记录文档 `notes/product_search_development_log.md` 或提交 Issue。

---

**开发完成日期**: 2025-08-05  
**版本**: 1.0.0  
**状态**: ✅ 完成
