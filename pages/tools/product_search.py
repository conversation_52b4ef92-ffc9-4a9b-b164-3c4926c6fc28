"""
智能商品搜索页面
支持多语言语义搜索
"""

import streamlit as st
from utils.semantic_search_engine import SemanticSearchEngine
from utils.product_data_manager import ProductDataManager

# 页面配置
st.set_page_config(
    page_title="商品搜索",
    page_icon="🔍",
    layout="wide"
)

# 使用缓存资源装饰器
@st.cache_resource
def get_search_engine():
    """获取搜索引擎"""
    return SemanticSearchEngine()

@st.cache_resource
def get_product_manager():
    """获取商品数据管理器"""
    return ProductDataManager()

def main():
    """主函数"""
    st.title("🔍 智能商品搜索")
    st.markdown("支持中文、西班牙语、英语等多语言语义搜索")
    st.markdown("---")
    
    # 初始化搜索引擎
    search_engine = get_search_engine()
    product_manager = get_product_manager()
    
    # 检查索引状态
    index_info = search_engine.get_index_info()
    
    if not index_info['index_exists']:
        st.warning("⚠️ 搜索索引不存在，请先在商品管理页面构建索引")
        
        if st.button("🔄 现在构建索引"):
            with st.spinner("正在构建索引，请稍候..."):
                try:
                    success = search_engine.build_index(force_rebuild=True)
                    if success:
                        st.success("索引构建成功！")
                        st.rerun()
                    else:
                        st.error("索引构建失败")
                except Exception as e:
                    st.error(f"索引构建失败: {str(e)}")
        return
    
    # 显示搜索界面
    show_search_interface(search_engine, product_manager)

def show_search_interface(search_engine, product_manager):
    """显示搜索界面"""
    
    # 搜索框
    st.markdown("### 🔍 搜索商品")
    
    # 创建两列布局
    col1, col2 = st.columns([3, 1])
    
    with col1:
        query = st.text_input(
            "",
            placeholder="输入您要查找的商品，支持中文、西班牙语、英语...",
            help="例如：水、agua、water、用来切菜的东西、cuchillo、knife"
        )
    
    with col2:
        search_button = st.button("搜索", type="primary", use_container_width=True)
    
    # 搜索结果数量选择
    result_count = st.slider("显示结果数量", min_value=1, max_value=10, value=5)
    
    # 执行搜索
    if query and (search_button or query):
        with st.spinner("正在搜索..."):
            try:
                results = search_engine.search(query, top_k=result_count)
                display_search_results(results, query)
            except Exception as e:
                st.error(f"搜索失败: {str(e)}")
                st.info("💡 提示：如果搜索失败，请尝试重新构建索引")
    
    # 显示搜索示例
    show_search_examples()

def display_search_results(results, query):
    """显示搜索结果"""
    
    if not results:
        st.warning(f"没有找到与 '{query}' 相关的商品")
        return
    
    st.markdown(f"### 🎯 搜索结果 (共找到 {len(results)} 个相关商品)")
    st.markdown("---")
    
    for i, (product, score) in enumerate(results, 1):
        # 创建结果卡片
        with st.container():
            col1, col2, col3 = st.columns([0.5, 2, 1])
            
            with col1:
                # 相似度分数
                score_color = "green" if score > 0.7 else "orange" if score > 0.5 else "red"
                st.markdown(f"**#{i}**")
                st.markdown(f"<span style='color: {score_color}'>相似度: {score:.2f}</span>", 
                           unsafe_allow_html=True)
            
            with col2:
                # 商品信息
                st.markdown(f"**🏷️ {product['product_name_zh']}**")
                
                if product.get('product_name_es'):
                    st.markdown(f"*{product['product_name_es']}*")
                
                # 位置信息（突出显示）
                st.markdown(f"📍 **位置**: {product['location']}")
                
                # 标签
                if product.get('tags'):
                    tags = product['tags'].split(',')
                    tag_html = " ".join([f"<span style='background-color: #f0f2f6; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;'>{tag.strip()}</span>" for tag in tags[:5]])
                    st.markdown(f"🏷️ {tag_html}", unsafe_allow_html=True)
            
            with col3:
                # 商品ID
                st.markdown(f"**ID**: {product['product_id']}")
        
        st.markdown("---")

def show_search_examples():
    """显示搜索示例"""
    
    st.markdown("### 💡 搜索示例")
    
    # 创建示例按钮
    examples = [
        ("水", "中文搜索"),
        ("agua", "西班牙语搜索"),
        ("water", "英语搜索"),
        ("用来切菜的东西", "语义搜索"),
        ("cuchillo", "西班牙语工具"),
        ("洗头发的", "中文描述"),
        ("fruta", "西班牙语类别"),
        ("something to drink", "英语描述")
    ]
    
    st.markdown("点击下面的示例来体验多语言搜索：")
    
    # 创建按钮网格
    cols = st.columns(4)
    for i, (example, description) in enumerate(examples):
        with cols[i % 4]:
            if st.button(f"{example}", help=description, key=f"example_{i}"):
                # 设置查询并触发搜索
                st.session_state.search_query = example
                st.rerun()
    
    # 处理示例搜索
    if 'search_query' in st.session_state:
        query = st.session_state.search_query
        del st.session_state.search_query
        
        with st.spinner(f"正在搜索 '{query}'..."):
            try:
                search_engine = get_search_engine()
                results = search_engine.search(query, top_k=5)
                
                st.markdown(f"### 示例搜索结果: '{query}'")
                display_search_results(results, query)
            except Exception as e:
                st.error(f"示例搜索失败: {str(e)}")
    
    # 使用说明
    st.markdown("---")
    st.markdown("### 📖 使用说明")
    
    with st.expander("如何使用智能搜索？"):
        st.markdown("""
        **多语言支持**:
        - 🇨🇳 中文：水、苹果、菜刀、洗发水
        - 🇪🇸 西班牙语：agua、manzana、cuchillo、champú
        - 🇬🇧 英语：water、apple、knife、shampoo
        
        **语义搜索**:
        - 不需要精确匹配商品名称
        - 可以用描述性语言搜索，如"用来切菜的东西"
        - 系统会理解语义并找到相关商品
        
        **搜索技巧**:
        - 使用简单明确的词语
        - 可以混合使用不同语言
        - 尝试用功能或用途来描述商品
        """)
    
    with st.expander("相似度分数说明"):
        st.markdown("""
        - 🟢 **0.7-1.0**: 高度相关，很可能是您要找的商品
        - 🟡 **0.5-0.7**: 中等相关，可能相关
        - 🔴 **0.0-0.5**: 低相关性，可能不是您要找的
        
        相似度分数基于语义相似性计算，分数越高表示与搜索查询越相关。
        """)

if __name__ == "__main__":
    main()
