"""
商品管理页面
用于商品信息的增删查改
"""

import streamlit as st
import pandas as pd
from utils.product_data_manager import ProductDataManager
from utils.semantic_search_engine import SemanticSearchEngine

# 页面配置
st.set_page_config(
    page_title="商品管理",
    page_icon="📦",
    layout="wide"
)

# 使用缓存资源装饰器
@st.cache_resource
def get_product_manager():
    """获取商品数据管理器"""
    return ProductDataManager()

@st.cache_resource
def get_search_engine():
    """获取搜索引擎"""
    return SemanticSearchEngine()

def main():
    """主函数"""
    st.title("📦 商品管理系统")
    st.markdown("---")
    
    # 初始化管理器
    product_manager = get_product_manager()
    search_engine = get_search_engine()
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📋 商品列表", "➕ 添加商品", "🗑️ 删除商品", "🔧 索引管理"])
    
    with tab1:
        show_product_list(product_manager)
    
    with tab2:
        add_product_form(product_manager, search_engine)
    
    with tab3:
        delete_product_form(product_manager, search_engine)
    
    with tab4:
        manage_search_index(search_engine)

def show_product_list(product_manager):
    """显示商品列表"""
    st.header("商品列表")
    
    # 加载商品数据
    df = product_manager.load_products()
    
    if len(df) == 0:
        st.info("暂无商品数据")
        return
    
    # 显示统计信息
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("商品总数", len(df))
    with col2:
        # 计算有西班牙语名称的商品数量
        has_spanish = df['product_name_es'].notna() & (df['product_name_es'] != '')
        st.metric("有西班牙语名称", has_spanish.sum())
    with col3:
        # 计算有标签的商品数量
        has_tags = df['tags'].notna() & (df['tags'] != '')
        st.metric("有标签", has_tags.sum())
    
    st.markdown("---")
    
    # 搜索和过滤
    search_term = st.text_input("🔍 搜索商品", placeholder="输入商品名称、位置或标签进行搜索...")
    
    # 过滤数据
    if search_term:
        mask = (
            df['product_name_zh'].str.contains(search_term, case=False, na=False) |
            df['product_name_es'].str.contains(search_term, case=False, na=False) |
            df['location'].str.contains(search_term, case=False, na=False) |
            df['tags'].str.contains(search_term, case=False, na=False)
        )
        filtered_df = df[mask]
        st.info(f"找到 {len(filtered_df)} 个匹配的商品")
    else:
        filtered_df = df
    
    # 显示商品表格
    if len(filtered_df) > 0:
        # 重新排列列的顺序，使其更易读
        display_df = filtered_df[['product_id', 'product_name_zh', 'product_name_es', 'location', 'tags']].copy()
        display_df.columns = ['ID', '中文名称', '西班牙语名称', '位置', '标签']
        
        st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True
        )
    else:
        st.warning("没有找到匹配的商品")

def add_product_form(product_manager, search_engine):
    """添加商品表单"""
    st.header("添加新商品")
    
    with st.form("add_product_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            product_name_zh = st.text_input(
                "中文名称 *", 
                placeholder="请输入商品的中文名称",
                help="必填字段"
            )
            
            location = st.text_input(
                "商品位置 *", 
                placeholder="例如：过道1，货架A",
                help="必填字段，描述商品在店内的具体位置"
            )
        
        with col2:
            product_name_es = st.text_input(
                "西班牙语名称", 
                placeholder="请输入商品的西班牙语名称",
                help="选填字段"
            )
            
            tags = st.text_input(
                "标签", 
                placeholder="用逗号分隔，例如：饮料,水,饮品",
                help="选填字段，用于增强搜索效果"
            )
        
        submitted = st.form_submit_button("添加商品", type="primary")
        
        if submitted:
            # 验证必填字段
            if not product_name_zh.strip():
                st.error("请输入中文名称")
                return
            
            if not location.strip():
                st.error("请输入商品位置")
                return
            
            # 准备商品数据
            product_data = {
                'product_name_zh': product_name_zh.strip(),
                'location': location.strip()
            }
            
            if product_name_es.strip():
                product_data['product_name_es'] = product_name_es.strip()
            
            if tags.strip():
                product_data['tags'] = tags.strip()
            
            # 添加商品
            success, message = product_manager.add_product(product_data)
            
            if success:
                st.success(message)
                st.info("💡 提示：添加新商品后，建议重建搜索索引以获得最佳搜索效果")
                
                # 清除缓存以刷新数据
                st.cache_resource.clear()
                
                # 自动跳转到商品列表
                st.rerun()
            else:
                st.error(message)

def delete_product_form(product_manager, search_engine):
    """删除商品表单"""
    st.header("删除商品")
    
    # 加载商品数据
    df = product_manager.load_products()
    
    if len(df) == 0:
        st.info("暂无商品数据")
        return
    
    # 创建商品选择选项
    product_options = []
    for _, row in df.iterrows():
        option_text = f"ID:{row['product_id']} - {row['product_name_zh']} ({row['location']})"
        product_options.append((option_text, int(row['product_id'])))
    
    # 选择要删除的商品
    selected_option = st.selectbox(
        "选择要删除的商品",
        options=[opt[0] for opt in product_options],
        help="选择一个商品进行删除"
    )
    
    if selected_option:
        # 获取选中的商品ID
        selected_id = next(opt[1] for opt in product_options if opt[0] == selected_option)
        
        # 显示商品详情
        product = product_manager.get_product_by_id(selected_id)
        if product:
            st.markdown("### 商品详情")
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"**ID:** {product['product_id']}")
                st.write(f"**中文名称:** {product['product_name_zh']}")
                st.write(f"**位置:** {product['location']}")
            
            with col2:
                st.write(f"**西班牙语名称:** {product.get('product_name_es', '无')}")
                st.write(f"**标签:** {product.get('tags', '无')}")
        
        # 确认删除
        st.markdown("---")
        st.warning("⚠️ 删除操作不可撤销，请确认是否要删除此商品？")
        
        col1, col2 = st.columns([1, 4])
        with col1:
            if st.button("确认删除", type="primary"):
                success, message = product_manager.delete_product(selected_id)
                
                if success:
                    st.success(message)
                    st.info("💡 提示：删除商品后，建议重建搜索索引")
                    
                    # 清除缓存以刷新数据
                    st.cache_resource.clear()
                    
                    # 刷新页面
                    st.rerun()
                else:
                    st.error(message)

def manage_search_index(search_engine):
    """管理搜索索引"""
    st.header("搜索索引管理")
    
    # 获取索引信息
    index_info = search_engine.get_index_info()
    
    # 显示索引状态
    st.markdown("### 索引状态")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        status = "✅ 存在" if index_info['index_exists'] else "❌ 不存在"
        st.metric("索引状态", status)
    
    with col2:
        st.metric("商品数量", index_info['product_count'])
    
    with col3:
        st.metric("向量维度", index_info['index_dimension'])
    
    st.markdown("---")
    
    # 索引操作
    st.markdown("### 索引操作")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔄 重建索引", type="primary"):
            with st.spinner("正在重建索引，请稍候..."):
                try:
                    success = search_engine.build_index(force_rebuild=True)
                    if success:
                        st.success("索引重建成功！")
                        # 清除缓存
                        st.cache_resource.clear()
                        st.rerun()
                    else:
                        st.error("索引重建失败，请检查日志")
                except Exception as e:
                    st.error(f"索引重建失败: {str(e)}")
    
    with col2:
        if st.button("📊 加载现有索引"):
            with st.spinner("正在加载索引..."):
                try:
                    success = search_engine.load_index()
                    if success:
                        st.success("索引加载成功！")
                        st.rerun()
                    else:
                        st.warning("没有找到现有索引，请先构建索引")
                except Exception as e:
                    st.error(f"索引加载失败: {str(e)}")
    
    # 使用说明
    st.markdown("---")
    st.markdown("### 使用说明")
    st.info("""
    - **重建索引**: 根据当前商品数据重新构建搜索索引。当添加、删除或修改商品后需要执行此操作。
    - **加载现有索引**: 加载已存在的索引文件。通常在系统启动时自动执行。
    - **注意**: 索引构建可能需要一些时间，特别是在商品数量较多时。
    """)

if __name__ == "__main__":
    main()
