import streamlit as st
from utils.auth_utils import require_authentication

# 检查认证
require_authentication()

st.title("🏠 欢迎来到个人网站")
st.markdown("---")

# 显示欢迎信息
if st.session_state.get('authentication_status'):
    st.success(f"欢迎回来，{st.session_state.get('name')}！")
    
    # 显示功能概览
    st.markdown("## 🛠️ 可用工具")
    

        
    st.markdown("---")
    st.info("💡 请使用左侧导航菜单访问各种工具。")
else:
    st.warning("请先登录以访问所有功能。")
