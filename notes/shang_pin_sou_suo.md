好的，这是一份您可以直接交给 AI 编程助手（Coding Agent）的详细需求文档。它清晰地描述了项目目标、功能需求和必须使用的技术栈，同时给予了 AI 在代码组织上的灵活性。

---

### **项目需求：构建一个基于 Streamlit 的多语言语义商品定位系统**

**一、 项目目标**

开发一个 Web 应用程序，帮助大型百货商店的员工和顾客快速找到商品。该应用的核心功能是提供一个智能搜索框，用户可以使用多种语言（包括但不限于中文、西班牙语、英语等）通过关键词、同义词甚至商品描述来查询商品及其在店内的具体位置。

**二、 核心功能需求**

**1. 商品信息管理 (后台功能)**
   *   需要一个简单的界面来维护商品数据。
   *   数据应存储在一个本地的 `products.csv` 文件中。
   *   该 CSV 文件应至少包含以下列：
      *   `product_id` (商品的唯一标识符，可以是自增数字)
      *   `product_name_zh` (商品的中文名称，**必填**)
      *   `product_name_es` (商品的西班牙语名称，选填)
      *   `location` (商品在店内的位置，例如 "过道 15，货架 3"，**必填**)
      *   `tags` (与商品相关的标签或描述，用逗号分隔，例如 "饮料,水,饮品,bebida,agua"，用于增强搜索效果)
   *   管理界面应支持以下操作：
      *   **添加** 新商品。
      *   **查看** 当前所有商品的列表。
      *   (可选，建议实现) **删除** 指定的商品。

**2. 智能商品搜索 (前台功能)**
   *   这是应用的主要功能，界面应非常简洁，只有一个核心的文本输入框供用户查询。
   *   **必须支持语义搜索**：搜索功能不应依赖于精确的关键词匹配。例如，当用户搜索“用来切菜的东西”，系统应能返回“菜刀”或“厨师刀”等相关商品。
   *   **必须支持多语言**：用户可以用中文、西班牙语、英语、俄语等多种语言进行查询。系统应能理解不同语言下的相同语义。例如，搜索 “agua” (西班牙语) 和 “water” (英语) 都应能找到“矿泉水”这个商品。
   *   搜索结果应清晰地展示商品的 **名称** (建议同时展示中英文或中西文名称) 和 **位置**。位置信息需要突出显示。

**3. 索引构建**
   *   为了实现高效的语义搜索，系统需要一个索引构建机制。
   *   这个机制可以是一个独立的脚本，或者是在管理界面中的一个“重建索引”按钮。
   *   **索引流程**：
      1.  读取 `products.csv` 文件中的所有商品信息。
      2.  对于每件商品，将 `product_name_zh`, `product_name_es`, 和 `tags` 等文本信息合并成一个单一的、可供搜索的描述性字符串。
      3.  使用指定的语言模型将这些描述性字符串转换成数值向量（Embeddings）。
      4.  将所有商品的向量构建成一个 Faiss 索引。
      5.  将这个构建好的索引保存到本地文件（例如 `products.faiss`），以便搜索时直接加载。

**三、 技术栈与实现要求**

*   **应用程序框架**: **Streamlit**
*   **数据存储与处理**: 使用 **Pandas** 库来操作 `products.csv` 文件。
*   **语义向量生成**: 必须使用 **`sentence-transformers`** 库。
*   **多语言模型**: 必须使用预训练模型 **`paraphrase-multilingual-MiniLM-L12-v2`**。这个模型轻量且支持超过50种语言，非常适合此项目。
*   **高效相似性搜索**: 必须使用 **`faiss-cpu`** 库来创建和搜索向量索引。请务必使用 CPU 版本 (`faiss-cpu`)，因为部署环境没有 GPU。
*   **性能优化**: 在 Streamlit 应用中加载语言模型 (`SentenceTransformer`) 和 Faiss 索引时，**必须使用 `@st.cache_resource` 装饰器**。这可以确保这些昂贵的资源只在应用启动时加载一次，避免在用户每次交互时都重复加载，从而保证应用的响应速度和低内存占用。

**四、 最终交付物**

请提供完整的 Python 代码。你可以在一个单独的 `app.py` 文件中实现所有功能，或者根据逻辑拆分成多个文件。代码需要包含必要的注释，解释关键部分的逻辑。最终目标是得到一个可以通过 `streamlit run <your_app_file>.py` 命令直接运行的应用程序。