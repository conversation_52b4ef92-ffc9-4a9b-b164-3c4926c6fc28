"""
测试优化后的搜索功能
"""

from utils.product_data_manager import ProductDataManager
from utils.semantic_search_engine import SemanticSearchEngine

def test_data_structure():
    """测试新的数据结构"""
    print("=== 测试新的数据结构 ===")
    
    manager = ProductDataManager()
    df = manager.load_products()
    
    print(f"数据列: {list(df.columns)}")
    print(f"商品数量: {len(df)}")
    
    # 检查是否有 search_keywords 字段
    if 'search_keywords' in df.columns:
        print("✅ search_keywords 字段已添加")
        
        # 显示几个示例
        print("\n示例商品的搜索关键词:")
        for i, row in df.head(3).iterrows():
            print(f"\n{row['product_name_zh']}:")
            print(f"  关键词: {row.get('search_keywords', '无')}")
    else:
        print("❌ search_keywords 字段缺失")

def test_search_text_creation():
    """测试搜索文本创建"""
    print("\n=== 测试搜索文本创建 ===")
    
    search_engine = SemanticSearchEngine()
    manager = ProductDataManager()
    
    products = manager.get_all_products_for_search()
    
    if products:
        # 测试大米的搜索文本
        rice_product = None
        for product in products:
            if product['product_name_zh'] == '大米':
                rice_product = product
                break
        
        if rice_product:
            search_text = search_engine._create_search_text(rice_product)
            print(f"大米的搜索文本:")
            print(f"  {search_text}")
            print(f"  长度: {len(search_text)} 字符")
        else:
            print("未找到大米商品")

def test_search_simulation():
    """模拟搜索测试（不需要实际模型）"""
    print("\n=== 模拟搜索测试 ===")
    
    manager = ProductDataManager()
    products = manager.get_all_products_for_search()
    
    # 模拟搜索"米"
    query = "米"
    print(f"搜索查询: '{query}'")
    
    # 简单的文本匹配来模拟搜索结果
    matches = []
    for product in products:
        search_engine = SemanticSearchEngine()
        search_text = search_engine._create_search_text(product)
        
        # 计算简单的匹配分数
        score = 0
        if query in product['product_name_zh']:
            score += 10  # 商品名称直接匹配
        if query in search_text:
            score += search_text.count(query)  # 搜索文本中的出现次数
        
        if score > 0:
            matches.append((product, score))
    
    # 按分数排序
    matches.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n模拟搜索结果 (按相关性排序):")
    for i, (product, score) in enumerate(matches[:5], 1):
        print(f"{i}. {product['product_name_zh']} - 分数: {score}")

def main():
    """主测试函数"""
    print("🔍 测试优化后的搜索系统")
    print("=" * 50)
    
    try:
        # 测试数据结构
        test_data_structure()
        
        # 测试搜索文本创建
        test_search_text_creation()
        
        # 模拟搜索测试
        test_search_simulation()
        
        print("\n" + "=" * 50)
        print("✅ 优化测试完成!")
        
        print("\n📋 优化内容总结:")
        print("1. ✅ 添加了 search_keywords 字段")
        print("2. ✅ 优化了搜索文本创建逻辑")
        print("3. ✅ 增加了商品名称的权重")
        print("4. ✅ 修复了 Streamlit 空 label 警告")
        print("5. ✅ 更新了商品管理界面")
        
        print("\n🚀 下一步:")
        print("1. 在 Streamlit 应用中重建搜索索引")
        print("2. 测试搜索 '米' 的效果")
        print("3. 根据需要进一步调整关键词")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
